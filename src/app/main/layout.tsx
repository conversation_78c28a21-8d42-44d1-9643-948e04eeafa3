import { AppSidebar } from "@/components/dashbaord/app-sidebar";
import SidebarHeader from "@/components/dashbaord/header";

import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar";

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset>
        <SidebarHeader />
        <div className="flex flex-1 p-4">{children}</div>
      </SidebarInset>
    </SidebarProvider>
  );
}
