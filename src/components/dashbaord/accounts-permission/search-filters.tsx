"use client";

import { Search, SlidersHorizontal, Plus } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuCheckboxItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useState } from "react";
import { CreateUserModal } from "@/components/modals/create-user-modal";

export function SearchFilterBar() {
  const [open, setOpen] = useState(false);
  const [filters, setFilters] = useState({
    actif: false,
    inactif: false,
    administrateur: false,
    utilisateur: false,
    invité: false,
  });

  return (
    <div className="flex items-center gap-4 p-4">
      <div className="relative flex-1 max-w-sm">
        <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
        <Input type="search" placeholder="Recherche..." className="pl-9" />
      </div>

      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="outline"
            className="gap-2 cursor-pointer bg-transparent"
          >
            <SlidersHorizontal className="h-4 w-4" />
            Filtres
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="start" className="w-56">
          <DropdownMenuLabel>Statut</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuCheckboxItem
            checked={filters.actif}
            onCheckedChange={(checked) =>
              setFilters({ ...filters, actif: checked })
            }
          >
            Actif
          </DropdownMenuCheckboxItem>
          <DropdownMenuCheckboxItem
            checked={filters.inactif}
            onCheckedChange={(checked) =>
              setFilters({ ...filters, inactif: checked })
            }
          >
            Inactif
          </DropdownMenuCheckboxItem>

          <DropdownMenuSeparator />
          <DropdownMenuLabel>Rôle</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuCheckboxItem
            checked={filters.administrateur}
            onCheckedChange={(checked) =>
              setFilters({ ...filters, administrateur: checked })
            }
          >
            Administrateur
          </DropdownMenuCheckboxItem>
          <DropdownMenuCheckboxItem
            checked={filters.utilisateur}
            onCheckedChange={(checked) =>
              setFilters({ ...filters, utilisateur: checked })
            }
          >
            Utilisateur
          </DropdownMenuCheckboxItem>
          <DropdownMenuCheckboxItem
            checked={filters.invité}
            onCheckedChange={(checked) =>
              setFilters({ ...filters, invité: checked })
            }
          >
            Invité
          </DropdownMenuCheckboxItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <Button
        className="ml-auto gap-2 bg-secondary hover:bg-accent cursor-pointer"
        onClick={() => setOpen(true)}
      >
        <Plus className="h-4 w-4" />
        Ajouter un utilisateur
      </Button>
      {open && <CreateUserModal open={open} onOpenChange={setOpen} />}
    </div>
  );
}
