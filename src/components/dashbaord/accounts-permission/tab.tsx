"use client";

import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { AccountTable } from "./tables/account-table";

const tabs = [
  {
    key: "users",
    title: "Liste des utilisateurs",
    content: <AccountTable />,
  },
  {
    key: "roles",
    title: "Gérer les rôles & permissions",
    content: <div className="">Roles</div>,
  },
];

export function UserTabs() {
  return (
    <Tabs defaultValue="users" className="mt-4">
      <div className="border-b-2 border-gray-200 border-t-2">
        <TabsList className=" justify-start shadow-none rounded-none bg-transparent  py-6">
          {tabs.map((t) => (
            <TabsTrigger
              key={t.key}
              value={t.key}
              className="rounded-none cursor-pointer border-4 py-6 shadow-none text-gray-700 border-transparent data-[state=active]:border-b-secondary data-[state=active]:bg-transparent data-[state=active]:shadow-none data-[state=active]:text-secondary"
            >
              {t.title}
            </TabsTrigger>
          ))}
        </TabsList>
      </div>
      {tabs.map((t) => (
        <TabsContent key={t.key} value={t.key} className="border-none">
          {t.content}
        </TabsContent>
      ))}
    </Tabs>
  );
}
