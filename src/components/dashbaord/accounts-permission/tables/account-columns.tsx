"use client";

import type { ColumnDef } from "@tanstack/react-table";
import type { User } from "@/types/user";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Pencil, Trash2 } from "lucide-react";
import { format } from "date-fns";
import { fr } from "date-fns/locale";

export const usersColumns: ColumnDef<User>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Sélectionner tout"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Sélectionner la ligne"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "name",
    header: "NOM COMPLET",
    cell: ({ row }) => {
      const user = row.original;
      return (
        <div className="flex flex-col">
          <span className="font-medium">{user.name}</span>
          <span className="text-sm text-muted-foreground">{user.email}</span>
        </div>
      );
    },
  },
  {
    accessorKey: "phone",
    header: "TELEPHONE",
    cell: ({ row }) => <span>{row.getValue("phone")}</span>,
  },
  {
    accessorKey: "role",
    header: "RÔLE ATTRIBUÉ",
    cell: ({ row }) => <span>{row.getValue("role")}</span>,
  },
  {
    accessorKey: "dateAdded",
    header: "DATE D'AJOUT",
    cell: ({ row }) => {
      const date = row.getValue("dateAdded") as Date;
      return (
        <div className="flex flex-col">
          <span>{format(new Date(date), "dd MMM yyyy", { locale: fr })}</span>
          <span className="text-sm text-muted-foreground">
            {format(new Date(date), "HH:mm")}
          </span>
        </div>
      );
    },
  },
  {
    accessorKey: "status",
    header: "STATUT",
    cell: ({ row }) => {
      const status = row.getValue("status") as string;
      return (
        <Badge
          variant={status === "active" ? "default" : "destructive"}
          className={
            status === "active"
              ? "bg-emerald-100 text-emerald-700 hover:bg-emerald-100"
              : "bg-red-100 text-red-700 hover:bg-red-100"
          }
        >
          {status === "active" ? "Actif" : "Désactivé"}
        </Badge>
      );
    },
  },
  {
    id: "actions",
    header: "ACTION",
    cell: ({ row }) => {
      return (
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8 text-muted-foreground hover:text-foreground"
          >
            <Pencil className="h-4 w-4" />
            <span className="sr-only">Modifier</span>
          </Button>
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8 text-muted-foreground hover:text-destructive"
          >
            <Trash2 className="h-4 w-4" />
            <span className="sr-only">Supprimer</span>
          </Button>
        </div>
      );
    },
  },
];
