"use client";

import {
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableHead,
  TableCell,
} from "@/components/ui/table";
import { usersColumns } from "./account-columns";
import {
  useReactTable,
  flexRender,
  getCoreRowModel,
} from "@tanstack/react-table";
import { useState } from "react";
import { User } from "@/types/user";
import { SearchFilterBar } from "../search-filters";

export function AccountTable() {
  const [data, setData] = useState<User[]>([
    {
      id: "1",
      name: "<PERSON>",
      email: "<EMAIL>",
      phone: "**********",
      role: "Administrateur",
      dateAdded: new Date(),
      status: "active",
    },
    {
      id: "2",
      name: "<PERSON>",
      email: "<EMAIL>",
      phone: "**********",
      role: "Utilisateur",
      dateAdded: new Date(),
      status: "inactive",
    },
    {
      id: "3",
      name: "<PERSON>",
      email: "<EMAIL>",
      phone: "**********",
      role: "Administrateur",
      dateAdded: new Date(),
      status: "active",
    },
    {
      id: "4",
      name: "<PERSON>e",
      email: "<EMAIL>",
      phone: "**********",
      role: "Utilisateur",
      dateAdded: new Date(),
      status: "inactive",
    },
  ]);

  const table = useReactTable({
    data,
    columns: usersColumns,
    getCoreRowModel: getCoreRowModel(),
  });

  return (
    <div>
      <SearchFilterBar />
      <Table className="mt-4 border text-gray-700 rounded">
        <TableHeader>
          {table.getHeaderGroups().map((headerGroup) => (
            <TableRow key={headerGroup.id}>
              {headerGroup.headers.map((header) => {
                return (
                  <TableHead key={header.id} className="text-gray-400">
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                  </TableHead>
                );
              })}
            </TableRow>
          ))}
        </TableHeader>
        <TableBody>
          {table.getRowModel().rows.map((row) => (
            <TableRow key={row.id}>
              {row.getVisibleCells().map((cell) => (
                <TableCell key={cell.id}>
                  {flexRender(cell.column.columnDef.cell, cell.getContext())}
                </TableCell>
              ))}
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
