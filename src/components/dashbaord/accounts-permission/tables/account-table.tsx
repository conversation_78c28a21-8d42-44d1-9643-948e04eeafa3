"use client";

import {
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableHead,
  TableCell,
} from "@/components/ui/table";
import { usersColumns } from "./account-columns";
import {
  useReactTable,
  flexRender,
  getCoreRowModel,
} from "@tanstack/react-table";
import { useState } from "react";
import type { User } from "@/types/user";
import { SearchFilterBar } from "../search-filters";

export function AccountTable() {
  const [data, setData] = useState<User[]>([
    {
      id: "1",
      name: "<PERSON>",
      email: "<EMAIL>",
      phone: "**********",
      role: "Administrateur",
      dateAdded: new Date(),
      status: "active",
    },
    {
      id: "2",
      name: "<PERSON>",
      email: "<EMAIL>",
      phone: "**********",
      role: "Utilisateur",
      dateAdded: new Date(),
      status: "inactive",
    },
    {
      id: "3",
      name: "<PERSON>",
      email: "<EMAIL>",
      phone: "**********",
      role: "Administrateur",
      dateAdded: new Date(),
      status: "active",
    },
    {
      id: "4",
      name: "<PERSON>e",
      email: "<EMAIL>",
      phone: "**********",
      role: "Utilisateur",
      dateAdded: new Date(),
      status: "inactive",
    },
    {
      id: "5",
      name: "John Doe",
      email: "<EMAIL>",
      phone: "**********",
      role: "Administrateur",
      dateAdded: new Date(),
      status: "active",
    },
    {
      id: "6",
      name: "Jane Doe",
      email: "<EMAIL>",
      phone: "**********",
      role: "Utilisateur",
      dateAdded: new Date(),
      status: "inactive",
    },
    {
      id: "7",
      name: "John Doe",
      email: "<EMAIL>",
      phone: "**********",
      role: "Administrateur",
      dateAdded: new Date(),
      status: "active",
    },
  ]);

  const table = useReactTable({
    data,
    columns: usersColumns,
    getCoreRowModel: getCoreRowModel(),
  });

  return (
    <div className="w-full">
      <SearchFilterBar />
      <div className="border border-gray-200 rounded">
        <Table className="mt-4  text-gray-700 rounded-lg ">
          <TableHeader className="p-4">
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead
                      key={header.id}
                      className="text-gray-400 px-6 py-4"
                    >
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody className="">
            {table.getRowModel().rows.map((row) => (
              <TableRow key={row.id} className="border">
                {row.getVisibleCells().map((cell) => (
                  <TableCell key={cell.id} className="px-6  py-4 border-b-none">
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
