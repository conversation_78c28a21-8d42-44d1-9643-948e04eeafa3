"use client";

import * as React from "react";

import { NavMain } from "@/components/dashbaord/nav-main";

import logo2 from "@/assets/icons/logo-2.png";
import mobileIcon from "@/assets/icons/logo.png";
import {
  DashboardIcon,
  LogoutIcon,
  LicenseIcon,
  UsersIcon,
  SupportIcon,
  SettingIcon,
} from "./icons";

import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenuItem,
  SidebarRail,
  SidebarTrigger,
  useSidebar,
} from "@/components/ui/sidebar";
import Image from "next/image";
import { cn } from "@/lib/utils";
import LogoutAction from "./logout-action";

const data = {
  user: {
    name: "shadcn",
    email: "<EMAIL>",
    avatar: "/avatars/shadcn.jpg",
  },
  navMain: [
    {
      title: "Tableau de bord",
      url: "/main",
      icon: DashboardIcon,
      isActive: true,
    },
    {
      title: "Gestion des licences",
      url: "/main/lisences",
      icon: LicenseIcon,
    },
    {
      title: "Compt<PERSON> & Accès",
      url: "/main/accounts",
      icon: UsersIcon,
    },
    {
      title: "Support & assistance",
      url: "/main/support",
      icon: SupportIcon,
    },
    {
      title: "Configuration générale",
      url: "/main/settings",
      icon: SettingIcon,
    },
  ],
};

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const { open } = useSidebar();
  return (
    <Sidebar collapsible="icon" {...props} className="border-none">
      <SidebarHeader
        className={cn("p-2 h-20 flex justify-center border-b", {
          "p-1 h-16": !open,
        })}
      >
        <div className="flex justify-between items-center">
          {open ? (
            <Image src={logo2} alt="logo" className="w-[70%]" />
          ) : (
            <Image src={mobileIcon} alt="mobile-icon" className="w-[70%]" />
          )}

          <SidebarTrigger className="-ml-1 bg-gray-100" />
        </div>
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={data.navMain} />
      </SidebarContent>

      <SidebarFooter>
        <SidebarContent>
          <SidebarMenuItem>
            <LogoutAction open={open} />
          </SidebarMenuItem>
        </SidebarContent>
      </SidebarFooter>

      <SidebarRail />
    </Sidebar>
  );
}
