"use client";
import React from "react";
import {
  B<PERSON><PERSON>rumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";
import { usePathname } from "next/navigation";
import { Bell, User, Settings, LogOut } from "lucide-react";

export default function SidebarHeader() {
  const pathname = usePathname();
  const paths = [
    {
      name: "Tableau de bord",
      url: "/main",
    },
    {
      name: "Gestion des licences",
      url: "/main/lisences",
    },
    {
      name: "Comptes & Accès",
      url: "/main/accounts",
    },
    {
      name: "Support & assistance",
      url: "/main/support",
    },
    {
      name: "Configuration générale",
      url: "/main/settings",
    },
  ];

  // Mock notifications data
  const notifications = [
    {
      id: 1,
      title: "Nouvelle licence",
      message: "Une nouvelle licence a été ajoutée",
      time: "Il y a 5 min",
    },
    {
      id: 2,
      title: "Compte expiré",
      message: "Le compte de John Doe expire bientôt",
      time: "Il y a 1h",
    },
    {
      id: 3,
      title: "Support requis",
      message: "Nouveau ticket de support #123",
      time: "Il y a 2h",
    },
    {
      id: 4,
      title: "Mise à jour",
      message: "Mise à jour système disponible",
      time: "Il y a 3h",
    },
    {
      id: 5,
      title: "Alerte",
      message: "Limite de licences atteinte",
      time: "Il y a 5h",
    },
  ];

  return (
    <header className="flex h-20 justify-between shrink-0 border-b items-center gap-2 transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12">
      <div className="flex items-center gap-2 px-4">
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem className="hidden md:block">
              <BreadcrumbLink href="/main" className="text-gray-300">
                Accueil
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator className="hidden md:block text-gray-400" />
            <BreadcrumbItem>
              <BreadcrumbPage className="text-gray-700">
                {paths.find((path) => path.url === pathname)?.name || "Page"}
              </BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
      </div>

      <div className="flex items-center gap-3 px-4">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <button
              aria-label="notifications"
              className="relative flex items-center justify-center w-10 h-10 border bg-white rounded-full  hover:bg-gray-50 transition-colors"
            >
              <span className="absolute top-0 right-0 flex items-center justify-center w-5 h-5 text-xs text-white bg-secondary rounded-full">
                {notifications.length}
              </span>
              <Bell className="w-5 h-5 text-slate-600" />
            </button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-80">
            <DropdownMenuLabel>Notifications</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <div className="max-h-[400px] overflow-y-auto">
              {notifications.map((notification) => (
                <DropdownMenuItem
                  key={notification.id}
                  className="flex flex-col items-start p-3 cursor-pointer"
                >
                  <div className="flex justify-between w-full">
                    <span className="font-semibold text-sm">
                      {notification.title}
                    </span>
                    <span className="text-xs text-gray-500">
                      {notification.time}
                    </span>
                  </div>
                  <span className="text-sm text-gray-600 mt-1">
                    {notification.message}
                  </span>
                </DropdownMenuItem>
              ))}
            </div>
            <DropdownMenuSeparator />
            <DropdownMenuItem className="text-center justify-center text-primary cursor-pointer">
              Voir toutes les notifications
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        <div className="hidden md:block bg-gray-200 h-8 w-px"></div>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <button className="flex items-center gap-2 hover:opacity-80 transition-opacity">
              <Avatar className="w-10 h-10 cursor-pointer">
                <AvatarImage
                  src="https://github.com/shadcn.png"
                  alt="User avatar"
                />
                <AvatarFallback>JD</AvatarFallback>
              </Avatar>
              <div className="hidden md:flex flex-col items-start">
                <span className="text-sm text-gray-700 font-semibold">
                  John Doe
                </span>
                <span className="text-xs text-gray-500">Administrateur</span>
              </div>
            </button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-56">
            <DropdownMenuLabel>Mon compte</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem className="cursor-pointer">
              <User className="mr-2 h-4 w-4" />
              <span>Profil</span>
            </DropdownMenuItem>
            <DropdownMenuItem className="cursor-pointer">
              <Settings className="mr-2 h-4 w-4" />
              <span>Paramètres</span>
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem className="cursor-pointer text-red-600">
              <LogOut className="mr-2 h-4 w-4" />
              <span>Déconnexion</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </header>
  );
}
