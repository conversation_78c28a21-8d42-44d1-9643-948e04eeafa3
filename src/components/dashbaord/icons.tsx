interface IconProps {
  fill?: string;
  size?: number;
}

export const LicenseIcon = ({ fill = "#4f566e", size = 26 }: IconProps) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={size}
    height={size}
    viewBox="0 0 26.266 26.266"
  >
    <path
      id="Vector"
      d="M5.8,6.108a.916.916,0,1,0,0,1.832Zm9.773,1.832a.916.916,0,0,0,0-1.832ZM5.8,12.217a.916.916,0,1,0,0,1.832Zm9.773,1.832a.916.916,0,0,0,0-1.832ZM5.8,18.325a.916.916,0,1,0,0,1.832Zm4.887,1.832a.916.916,0,1,0,0-1.832ZM4.581,1.832H16.8V0H4.581Zm18.325,22.6H4.581v1.832H22.906ZM1.832,21.684V4.581H0v17.1ZM20.463,9.162h2.443V7.33H20.463Zm3.97,1.527V22.906h1.832V10.689ZM21.379,22.906V8.246H19.547v14.66Zm1.527,1.527a1.527,1.527,0,0,1-1.527-1.527H19.547a3.36,3.36,0,0,0,3.36,3.36Zm1.527-1.527a1.527,1.527,0,0,1-1.527,1.527v1.832a3.36,3.36,0,0,0,3.36-3.36ZM19.547,4.581V8.246h1.832V4.581ZM4.581,24.433a2.749,2.749,0,0,1-2.749-2.749H0a4.581,4.581,0,0,0,4.581,4.581ZM16.8,1.832a2.749,2.749,0,0,1,2.749,2.749h1.832A4.581,4.581,0,0,0,16.8,0Zm6.108,7.33a1.527,1.527,0,0,1,1.527,1.527h1.832a3.36,3.36,0,0,0-3.36-3.36ZM4.581,0A4.581,4.581,0,0,0,0,4.581H1.832A2.749,2.749,0,0,1,4.581,1.832ZM5.8,7.941h9.773V6.108H5.8Zm0,6.108h9.773V12.217H5.8Zm0,6.108h4.887V18.325H5.8Z"
      fill={fill}
    />
  </svg>
);

export const SupportIcon = ({ fill = "#4f566e", size = 24 }: IconProps) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={size}
    height={size}
    viewBox="0 0 20.753 25.366"
  >
    <path
      id="user-headset_1_"
      data-name="user-headset (1)"
      d="M9.918,11.377a7.2,7.2,0,0,0,.134,1.486,1.153,1.153,0,0,1-2.254.485,9.363,9.363,0,0,1-.186-1.972c0-3.933,1.832-5.765,5.765-5.765s5.765,1.832,5.765,5.765a1.153,1.153,0,1,1-2.306,0c0-2.651-.808-3.459-3.459-3.459s-3.459.808-3.459,3.459ZM13.377,1C6.1,1,3,4.1,3,11.377a1.153,1.153,0,1,0,2.306,0c0-5.959,2.112-8.071,8.071-8.071s8.071,2.111,8.071,8.071c0,2.651-.808,3.459-3.459,3.459H15.683c0-1.648-.658-2.306-2.306-2.306s-2.306.658-2.306,2.306.658,2.306,2.306,2.306h4.612c3.933,0,5.765-1.832,5.765-5.765C23.753,4.1,20.65,1,13.377,1Zm0,18.446c-3.163,0-7.52.566-8.832,5.466a1.153,1.153,0,0,0,2.228.6c.522-1.951,1.684-3.758,6.6-3.758s6.082,1.806,6.6,3.758a1.153,1.153,0,0,0,2.228-.6c-1.312-4.9-5.669-5.466-8.832-5.466Z"
      transform="translate(-3 -1)"
      fill={fill}
    />
  </svg>
);

export const SettingIcon = ({ fill = "#4f566e", size = 25 }: IconProps) => (
  <svg
    id="settings"
    xmlns="http://www.w3.org/2000/svg"
    width={size}
    height={size}
    viewBox="0 0 25 25.011"
  >
    <path
      id="Tracé_15608"
      data-name="Tracé 15608"
      d="M12.742,8.329A3.825,3.825,0,0,0,9.706,9.7c-1.858,1.859-1.858,3.743,0,5.6a4.125,4.125,0,0,0,2.563,1.376A3.937,3.937,0,0,0,15.3,15.3c1.858-1.858,1.857-3.743,0-5.6a4.18,4.18,0,0,0-2.563-1.376ZM13.7,13.7h0a2.078,2.078,0,0,1-1.253.715,2.1,2.1,0,0,1-1.129-.715c-.895-.895-1.02-1.364,0-2.385a2.094,2.094,0,0,1,1.206-.716h.047a2.1,2.1,0,0,1,1.129.715C14.591,12.206,14.717,12.676,13.7,13.7Z"
      transform="translate(-0.005 0.001)"
      fill={fill}
    />
    <path
      id="Tracé_15609"
      data-name="Tracé 15609"
      d="M26.006,13.454a3.222,3.222,0,0,0-2.923-3.468q-.188-.358-.414-.72a3.237,3.237,0,0,0-.282-4.645,3.234,3.234,0,0,0-4.647-.279q-.356-.225-.713-.409A3.218,3.218,0,0,0,13.585,1a3.284,3.284,0,0,0-3.509,2.983q-.355.186-.715.412a3.244,3.244,0,0,0-4.654.309A3.246,3.246,0,0,0,4.4,9.357q-.227.362-.416.72a3.246,3.246,0,0,0-2.977,3.483,3.222,3.222,0,0,0,2.923,3.468c.125.239.263.479.415.72a3.236,3.236,0,0,0,.281,4.645,3.234,3.234,0,0,0,4.647.279q.356.225.713.409a3.218,3.218,0,0,0,3.442,2.933,3.263,3.263,0,0,0,3.509-2.983q.355-.186.715-.412a3.251,3.251,0,0,0,4.652-.309,3.238,3.238,0,0,0,.31-4.652q.227-.362.416-.719a3.246,3.246,0,0,0,2.977-3.483Zm-3.83,1.3a1.136,1.136,0,0,0-.943.674,8.685,8.685,0,0,1-.954,1.613,1.14,1.14,0,0,0,.081,1.443c1.22,1.326.811,1.736.335,2.211-.495.5-.887.887-2.209-.337a1.14,1.14,0,0,0-1.444-.082,8.511,8.511,0,0,1-1.61.948,1.139,1.139,0,0,0-.676.945c-.134,1.558-.682,1.561-1.315,1.563s-1.157.007-1.276-1.508a1.135,1.135,0,0,0-.686-.956,8.116,8.116,0,0,1-1.6-.938,1.138,1.138,0,0,0-1.446.08c-1.34,1.236-1.742.832-2.2.368s-.866-.868.366-2.206a1.135,1.135,0,0,0,.077-1.446,8.167,8.167,0,0,1-.944-1.6,1.136,1.136,0,0,0-.954-.684c-1.506-.121-1.5-.631-1.5-1.277s.006-1.182,1.557-1.317a1.136,1.136,0,0,0,.943-.674,8.782,8.782,0,0,1,.954-1.615,1.14,1.14,0,0,0-.081-1.443c-1.22-1.325-.81-1.735-.335-2.21.495-.495.887-.886,2.209.335a1.137,1.137,0,0,0,1.443.082,8.615,8.615,0,0,1,1.611-.948,1.139,1.139,0,0,0,.676-.945c.134-1.558.682-1.561,1.315-1.563.651,0,1.157-.007,1.276,1.508a1.135,1.135,0,0,0,.686.956,8.117,8.117,0,0,1,1.6.938A1.138,1.138,0,0,0,18.571,6.6c1.34-1.236,1.741-.832,2.2-.368s.868.868-.366,2.206a1.137,1.137,0,0,0-.078,1.446,8.167,8.167,0,0,1,.944,1.6,1.136,1.136,0,0,0,.954.684c1.506.121,1.5.631,1.5,1.277s-.006,1.182-1.557,1.317Z"
      transform="translate(-1.006 -1.001)"
      fill={fill}
    />
  </svg>
);

export const LogoutIcon = ({ fill = "#d93a59", size = 25 }: IconProps) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={size}
    height={size}
    viewBox="0 0 25.001 25.887"
  >
    <g id="power" transform="translate(-1.376 -1)">
      <path
        id="Tracé_15610"
        data-name="Tracé 15610"
        d="M12.265,9.566A1.764,1.764,0,0,0,14.03,7.8V2.765a1.765,1.765,0,0,0-3.53,0V7.8a1.764,1.764,0,0,0,1.765,1.765Z"
        transform="translate(1.612)"
        fill={fill}
      />
      <path
        id="Tracé_15611"
        data-name="Tracé 15611"
        d="M21.456,3.129a1.765,1.765,0,0,0-1.765,3.058c1.354.781,3.156,2.445,3.156,7.844,0,7.488-3.432,8.993-8.97,8.993s-8.971-1.5-8.971-8.993c0-5.379,1.8-7.047,3.159-7.834A1.765,1.765,0,0,0,6.293,3.144C2.986,5.063,1.376,8.625,1.376,14.029c0,11.226,7.134,12.523,12.5,12.523s12.5-1.3,12.5-12.523c0-5.425-1.61-8.991-4.921-10.9Z"
        transform="translate(0 0.334)"
        fill={fill}
      />
    </g>
  </svg>
);

export const DashboardIcon = ({ fill = "#4f566e", size = 29 }: IconProps) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    className="font-bold"
    width={size}
    height={size}
    viewBox="0 0 29 30"
  >
    <defs>
      <clipPath id="clip-path">
        <rect
          id="Rectangle_7157"
          data-name="Rectangle 7157"
          width="29"
          height="30"
          transform="translate(0 0.001)"
          fill={fill}
        />
      </clipPath>
    </defs>
    <g
      id="Groupe_8838"
      data-name="Groupe 8838"
      transform="translate(0 -0.001)"
      clipPath="url(#clip-path)"
    >
      <path
        id="Tracé_13995"
        data-name="Tracé 13995"
        d="M30,175.229A1.229,1.229,0,0,1,31.229,174h4.914a1.229,1.229,0,0,1,1.229,1.229v4.914a1.229,1.229,0,0,1-1.229,1.229H31.229A1.229,1.229,0,0,1,30,180.143Z"
        transform="translate(-25.468 -169.326)"
        fill="none"
        stroke={fill}
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth="2"
      />
      <path
        id="Tracé_13996"
        data-name="Tracé 13996"
        d="M40,175.229A1.229,1.229,0,0,1,41.229,174h4.914a1.229,1.229,0,0,1,1.229,1.229v4.914a1.229,1.229,0,0,1-1.229,1.229H41.229A1.229,1.229,0,0,1,40,180.143Z"
        transform="translate(-23.181 -169.326)"
        fill="none"
        stroke={fill}
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth="2"
      />
      <path
        id="Tracé_13997"
        data-name="Tracé 13997"
        d="M30,185.229A1.229,1.229,0,0,1,31.229,184h4.914a1.229,1.229,0,0,1,1.229,1.229v4.914a1.229,1.229,0,0,1-1.229,1.229H31.229A1.229,1.229,0,0,1,30,190.143Z"
        transform="translate(-25.468 -167.04)"
        fill="none"
        stroke={fill}
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth="2"
      />
      <path
        id="Tracé_13998"
        data-name="Tracé 13998"
        d="M40,185.229A1.229,1.229,0,0,1,41.229,184h4.914a1.229,1.229,0,0,1,1.229,1.229v4.914a1.229,1.229,0,0,1-1.229,1.229H41.229A1.229,1.229,0,0,1,40,190.143Z"
        transform="translate(-23.181 -167.04)"
        fill="none"
        stroke={fill}
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth="2"
      />
    </g>
  </svg>
);

export const UsersIcon = ({ fill = "#4f566e", size = 24 }: IconProps) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={size}
    height={size}
    viewBox="0 0 23.949 24.001"
  >
    <path
      id="employees_1_"
      data-name="employees (1)"
      d="M13.5,12a4,4,0,1,0,4-4A4,4,0,0,0,13.5,12Zm4-2a2,2,0,1,1-2,2A2,2,0,0,1,17.5,10ZM12,8A4,4,0,1,0,8,4,4,4,0,0,0,12,8Zm0-6a2,2,0,1,1-2,2A2,2,0,0,1,12,2ZM6.5,16a4,4,0,1,0-4-4A4,4,0,0,0,6.5,16Zm0-6a2,2,0,1,1-2,2A2,2,0,0,1,6.5,10ZM23.971,22.91A1,1,0,0,1,23.065,24c-.03,0-.061,0-.091,0a1,1,0,0,1-1-.91,4.475,4.475,0,0,0-1.721-3.118.144.144,0,0,0-.16.049l-1.841,2.133a.9.9,0,0,1-.757.347,1,1,0,0,1-.752-.34l-1.865-2.126a.14.14,0,0,0-.16-.048,4.419,4.419,0,0,0-1.7,3.1.982.982,0,0,1-.873.881c-.029,0-.054.022-.084.025s-.061,0-.091,0c-.01,0-.018,0-.027,0s-.007,0-.011,0-.02-.008-.031-.009a.991.991,0,0,1-.926-.9,4.413,4.413,0,0,0-1.72-3.118.14.14,0,0,0-.16.049L7.258,22.155a1,1,0,0,1-1.509.006L3.884,20.035c-.052-.058-.134-.069-.16-.047a4.416,4.416,0,0,0-1.7,3.1,1,1,0,1,1-1.992-.18,6.489,6.489,0,0,1,2.46-4.5,2.119,2.119,0,0,1,2.9.3l1.107,1.262,1.09-1.263a2.116,2.116,0,0,1,2.9-.322A6.5,6.5,0,0,1,12,20.078a6.534,6.534,0,0,1,1.489-1.665,2.119,2.119,0,0,1,2.9.3l1.107,1.261,1.09-1.263a2.118,2.118,0,0,1,2.9-.323,6.483,6.483,0,0,1,2.487,4.519Z"
      transform="translate(-0.026)"
      fill={fill}
    />
  </svg>
);
