"use client";
import React from "react";
import { LogoutIcon } from "./icons";
import { LogoutConfirmation } from "../modals/logout-confirmation";

export default function LogoutAction({ open }: { open: boolean }) {
  const [isOpen, setIsOpen] = React.useState(false);
  return (
    <>
      <button
        className="flex items-center gap-2 p-2 cursor-pointer border-t"
        onClick={() => setIsOpen(true)}
      >
        <LogoutIcon />
        {open && <span className="text-red-600">Déconnexion</span>}
      </button>
      {isOpen && <LogoutConfirmation open={isOpen} onOpenChange={setIsOpen} />}
    </>
  );
}
