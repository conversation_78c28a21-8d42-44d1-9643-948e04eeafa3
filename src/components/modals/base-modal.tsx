"use client";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { twMerge } from "tailwind-merge";

interface BaseModalProps {
  children: React.ReactNode;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  title?: string;
  subtitle?: string;
  className?: string;
}

export function BaseModal({
  children,
  open,
  onOpenChange,
  title,
  subtitle,
  className,
}: BaseModalProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        className={twMerge("p-0 text-gray-700", className)}
        // showCloseButton={false}
      >
        {(title || subtitle) && (
          <DialogHeader className="border-b-2 p-6">
            {title && <DialogTitle>{title}</DialogTitle>}
            {subtitle && <DialogDescription>{subtitle}</DialogDescription>}
          </DialogHeader>
        )}
        <div className="p-6">{children}</div>
      </DialogContent>
    </Dialog>
  );
}
