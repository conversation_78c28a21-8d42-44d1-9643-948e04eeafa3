"use client";
import React, { useState } from "react";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { BaseModal } from "./base-modal";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { X, ArrowRight } from "lucide-react";
import { PhoneInput } from "../ui/phone-input";

interface CreateUserModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit?: (data: FormData) => void;
}

// Main schema with all fields
const fullSchema = z.object({
  name: z.string().min(2, "Nom complet doit contenir au moins 2 caractères"),
  email: z.email("Email invalide"),
  phone: z.string().optional(),
  role: z.string().min(1, "Veuillez sélectionner un rôle"),
  sendLoginInfo: z.boolean(),
});

// Step 1 schema - only the fields needed for step 1
const step1Schema = fullSchema.pick({
  name: true,
  email: true,
  phone: true,
  role: true,
});

// Step 2 schema - only the fields needed for step 2
const step2Schema = fullSchema.pick({
  sendLoginInfo: true,
});

type FormData = z.infer<typeof fullSchema>;
type Step1Data = z.infer<typeof step1Schema>;
type Step2Data = z.infer<typeof step2Schema>;

export function CreateUserModal({
  open,
  onOpenChange,
  onSubmit,
}: CreateUserModalProps) {
  const [currentStep, setCurrentStep] = useState(1);
  const [step1Data, setStep1Data] = useState<Step1Data | null>(null);

  // Form for step 1
  const step1Form = useForm<Step1Data>({
    resolver: zodResolver(step1Schema),
    defaultValues: {
      name: "",
      email: "",
      phone: "",
      role: "",
      sendLoginInfo: true,
    },
  });

  // Form for step 2
  const step2Form = useForm<Step2Data>({
    resolver: zodResolver(step2Schema),
    defaultValues: {
      sendLoginInfo: true,
    },
  });

  const handleStep1Submit = (data: Step1Data) => {
    setStep1Data(data);
    setCurrentStep(2);
  };

  const handleStep2Submit = (data: Step2Data) => {
    if (step1Data) {
      const fullData: FormData = {
        ...step1Data,
        ...data,
      };
      console.log(fullData);
      onSubmit?.(fullData);
      onOpenChange(false);
      handleClose();
    }
  };

  const handleClose = () => {
    setCurrentStep(1);
    setStep1Data(null);
    step1Form.reset();
    step2Form.reset();
  };

  const handleBack = () => {
    setCurrentStep(1);
  };

  return (
    <BaseModal
      open={open}
      title="Création d'un nouvel utilisateur"
      className="min-w-2xl"
      onOpenChange={onOpenChange}
    >
      <div className="w-full">
        <div className="flex mb-8 border-b">
          <div className="flex-1">
            <div
              className={`w-full text-left pb-3 border-b-2 transition-colors ${
                currentStep === 1
                  ? "border-secondary text-secondary"
                  : "border-transparent text-gray-500"
              }`}
            >
              <span className="text-sm font-medium">
                1. Informations générales
              </span>
            </div>
          </div>
          <div className="flex-1">
            <div
              className={`w-full text-left pb-3 border-b-2 transition-colors ${
                currentStep === 2
                  ? "border-secondary text-secondary"
                  : "border-transparent text-gray-500"
              }`}
            >
              <span className="text-sm font-medium">
                2. Informations supplémentaires
              </span>
            </div>
          </div>
        </div>

        {/* Step 1 Form */}
        {currentStep === 1 && (
          <Form {...step1Form}>
            <form
              onSubmit={step1Form.handleSubmit(handleStep1Submit)}
              className="space-y-6"
            >
              <div className="space-y-6">
                {/* Name Field */}
                <FormField
                  control={step1Form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-sm font-medium text-gray-900">
                        Nom complet du responsable
                      </FormLabel>
                      <FormControl>
                        <Input
                          type="text"
                          placeholder="Nom complet"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Email Field */}
                  <FormField
                    control={step1Form.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sm font-medium text-gray-900">
                          Email
                        </FormLabel>
                        <FormControl>
                          <Input type="email" placeholder="Email" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Phone Field */}
                  <FormField
                    control={step1Form.control}
                    name="phone"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sm font-medium text-gray-900">
                          Téléphone (optionnel)
                        </FormLabel>
                        <FormControl>
                          <PhoneInput
                            onChange={field.onChange}
                            value={field.value}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Role Field */}
                <FormField
                  control={step1Form.control}
                  name="role"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-sm font-medium text-gray-900">
                        Type de compte
                      </FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        value={field.value}
                      >
                        <FormControl>
                          <SelectTrigger className="px-4 py-3">
                            <SelectValue placeholder="Sélectionner" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="administrateur">
                            Administrateur
                          </SelectItem>
                          <SelectItem value="gestionnaire">
                            Gestionnaire
                          </SelectItem>
                          <SelectItem value="utilisateur">
                            Utilisateur
                          </SelectItem>
                          <SelectItem value="observateur">
                            Observateur
                          </SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Footer */}
              <div className="flex justify-between pt-6">
                <Button
                  type="button"
                  variant="ghost"
                  onClick={() => {
                    onOpenChange(false);
                    handleClose();
                  }}
                  className="px-6"
                >
                  <X className="w-4 h-4 mr-2" />
                  Fermer
                </Button>
                <Button
                  type="submit"
                  className="px-6 bg-secondary hover:bg-secondary/80 text-white"
                >
                  Suivant
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Button>
              </div>
            </form>
          </Form>
        )}

        {/* Step 2 Form */}
        {currentStep === 2 && (
          <Form {...step2Form}>
            <form
              onSubmit={step2Form.handleSubmit(handleStep2Submit)}
              className="space-y-6"
            >
              <div className="space-y-6">
                {/* Send Login Info Checkbox */}
                <FormField
                  control={step2Form.control}
                  name="sendLoginInfo"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4 bg-secondary/5">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                          className="mt-0.5"
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel className="text-sm font-medium text-gray-900 cursor-pointer">
                          Envoyer les informations de connexion par email/SMS
                        </FormLabel>
                      </div>
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={step1Data.control}
                name="sendLoginInfo"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4 bg-secondary/5">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                        className="mt-0.5"
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel className="text-sm font-medium text-gray-900 cursor-pointer">
                        Envoyer les informations de connexion par email/SMS
                      </FormLabel>
                    </div>
                  </FormItem>
                )}
              />

              {/* Footer */}
              <div className="flex justify-between pt-6">
                <Button
                  type="button"
                  variant="ghost"
                  onClick={handleBack}
                  className="px-6"
                >
                  Précédent
                </Button>
                <Button
                  type="submit"
                  className="px-6 bg-orange-500 hover:bg-orange-600 text-white"
                >
                  Créer l&apos;utilisateur
                </Button>
              </div>
            </form>
          </Form>
        )}
      </div>
    </BaseModal>
  );
}
