"use client";
import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { BaseModal } from "./base-modal";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { X, ArrowRight } from "lucide-react";

// Form schema
const createUserSchema = z.object({
  fullName: z
    .string({
      message: "Le nom complet est requis.",
    })
    .min(2, {
      message: "Le nom complet doit contenir au moins 2 caractères.",
    }),
  email: z.email({
    message: "L'adresse e-mail est invalide.",
  }),
  phone: z.string().optional(),
  accountType: z
    .string({
      message: "Le type de compte est requis.",
    })
    .min(1, {
      message: "Veuillez sélectionner un type de compte.",
    }),
  sendLoginInfo: z.boolean().default(true),
});

type CreateUserFormData = z.infer<typeof createUserSchema>;

interface CreateUserModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit?: (data: CreateUserFormData) => void;
}

export function CreateUserModal({
  open,
  onOpenChange,
  onSubmit,
}: CreateUserModalProps) {
  const [currentStep, setCurrentStep] = useState(1);

  const form = useForm<CreateUserFormData>({
    resolver: zodResolver(createUserSchema),
    defaultValues: {
      fullName: "",
      email: "",
      phone: "",
      accountType: "",
      sendLoginInfo: true,
    },
  });

  const handleNext = async () => {
    const isValid = await form.trigger(["fullName", "email", "accountType"]);
    if (isValid) {
      setCurrentStep(2);
    }
  };

  const handleFinalSubmit = (data: CreateUserFormData) => {
    onSubmit?.(data);
    onOpenChange(false);
    setCurrentStep(1);
    form.reset();
  };

  const handleClose = () => {
    onOpenChange(false);
    setCurrentStep(1);
    form.reset();
  };

  return (
    <BaseModal open={open} onOpenChange={onOpenChange} className="max-w-2xl">
      <div className="w-full">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-lg font-semibold text-gray-900">
            Création d'un nouvel utilisateur
          </h2>
          <button
            onClick={handleClose}
            className="p-1 hover:bg-gray-100 rounded-md transition-colors"
          >
            <X className="w-5 h-5 text-gray-500" />
          </button>
        </div>

        {/* Step Tabs */}
        <div className="flex mb-8">
          <div className="flex-1">
            <div
              className={`w-full text-left pb-3 border-b-2 transition-colors ${
                currentStep === 1
                  ? "border-orange-500 text-orange-600"
                  : "border-gray-200 text-gray-500"
              }`}
            >
              <span className="text-sm font-medium">
                1. Informations générales
              </span>
            </div>
          </div>
          <div className="flex-1">
            <div
              className={`w-full text-left pb-3 border-b-2 transition-colors ${
                currentStep === 2
                  ? "border-orange-500 text-orange-600"
                  : "border-gray-200 text-gray-500"
              }`}
            >
              <span className="text-sm font-medium">
                2. Informations supplémentaires
              </span>
            </div>
          </div>
        </div>

        {/* Form */}
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleFinalSubmit)}
            className="space-y-6"
          >
            {currentStep === 1 && (
              <div className="space-y-6">
                {/* Full Name */}
                <FormField
                  name="fullName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-sm font-medium text-gray-800">
                        Nom complet du responsable
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Nom complet"
                          {...field}
                          className="px-4 py-3 rounded border text-gray-700 placeholder:text-gray-400"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Email */}
                  <FormField
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sm font-medium text-gray-800">
                          Email
                        </FormLabel>
                        <FormControl>
                          <Input
                            type="email"
                            placeholder="Email"
                            {...field}
                            className="px-4 py-3 rounded border text-gray-700 placeholder:text-gray-400"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Phone */}
                  <FormField
                    name="phone"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sm font-medium text-gray-800">
                          Téléphone (optionnel)
                        </FormLabel>
                        <FormControl>
                          <div className="flex">
                            <div className="flex items-center px-3 bg-gray-50 border border-r-0 rounded-l">
                              <span className="text-sm">🇬🇦 +241</span>
                            </div>
                            <Input
                              type="tel"
                              placeholder="--- --- ----"
                              {...field}
                              className="rounded-l-none px-4 py-3 border text-gray-700 placeholder:text-gray-400"
                            />
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Account Type */}
                <FormField
                  name="accountType"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-sm font-medium text-gray-800">
                        Type de compte
                      </FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        value={field.value}
                      >
                        <FormControl>
                          <SelectTrigger className="px-4 py-3 rounded border text-gray-700">
                            <SelectValue placeholder="Sélectionner" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="admin">Administrateur</SelectItem>
                          <SelectItem value="manager">Gestionnaire</SelectItem>
                          <SelectItem value="user">Utilisateur</SelectItem>
                          <SelectItem value="viewer">Observateur</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            )}

            {currentStep === 2 && (
              <div className="space-y-6">
                {/* Send Login Info Checkbox */}
                <FormField
                  name="sendLoginInfo"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4 bg-orange-50">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                          className="mt-0.5"
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel className="text-sm font-medium text-gray-800">
                          Envoyer les informations de connexion par email/SMS
                        </FormLabel>
                      </div>
                    </FormItem>
                  )}
                />

                {/* Summary */}
                <div className="bg-gray-50 rounded-lg p-4 space-y-3">
                  <h3 className="font-medium text-gray-900">
                    Résumé des informations
                  </h3>
                  <div className="space-y-2 text-sm text-gray-600">
                    <p>
                      <span className="font-medium">Nom:</span>{" "}
                      {form.watch("fullName") || "Non renseigné"}
                    </p>
                    <p>
                      <span className="font-medium">Email:</span>{" "}
                      {form.watch("email") || "Non renseigné"}
                    </p>
                    {form.watch("phone") && (
                      <p>
                        <span className="font-medium">Téléphone:</span> +241{" "}
                        {form.watch("phone")}
                      </p>
                    )}
                    <p>
                      <span className="font-medium">Type de compte:</span>{" "}
                      {form.watch("accountType") || "Non sélectionné"}
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Footer */}
            <div className="flex justify-between pt-6">
              <Button
                type="button"
                variant="outline"
                onClick={
                  currentStep === 1 ? handleClose : () => setCurrentStep(1)
                }
                className="px-6"
              >
                {currentStep === 1 ? "Fermer" : "Précédent"}
              </Button>
              {currentStep === 1 ? (
                <Button
                  type="button"
                  onClick={handleNext}
                  className="px-6 bg-orange-500 hover:bg-orange-600 text-white"
                >
                  Suivant
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Button>
              ) : (
                <Button
                  type="submit"
                  className="px-6 bg-orange-500 hover:bg-orange-600 text-white"
                >
                  Créer l'utilisateur
                </Button>
              )}
            </div>
          </form>
        </Form>
      </div>
    </BaseModal>
  );
}
