"use client";
import { LogOut } from "lucide-react";
import { BaseModal } from "./base-modal";

interface LogoutConfirmationProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function LogoutConfirmation({
  open,
  onOpenChange,
}: LogoutConfirmationProps) {
  return (
    <BaseModal
      open={open}
      onOpenChange={onOpenChange}
      title="Déconnexion"
      subtitle="Confirmer la déconnexion"
    >
      <LogOut className="w-12 h-12 text-red-600 mx-auto" />
      <p>Êtes-vous sûr de vouloir vous déconnecter ?</p>
      <div className="flex justify-end gap-2 mt-4">
        <button
          className="px-4 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg"
          onClick={() => onOpenChange(false)}
        >
          Annuler
        </button>
        <button
          className="px-4 py-2 bg-red-600 text-white hover:bg-red-700 rounded-lg"
          onClick={() => onOpenChange(false)}
        >
          Déconnexion
        </button>
      </div>
    </BaseModal>
  );
}
